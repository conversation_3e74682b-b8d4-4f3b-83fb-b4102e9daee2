<picture>
  <source media="(prefers-color-scheme: dark)" srcset="https://neon.com/brand/neon-logo-dark-color.svg">
  <source media="(prefers-color-scheme: light)" srcset="https://neon.com/brand/neon-logo-light-color.svg">
  <img width="250px" alt="Neon Logo fallback" src="https://neon.com/brand/neon-logo-dark-color.svg">
</picture>

# Getting started with Neon, Next.js, Drizzle in Local and Vercel environments

## Clone the repository

```bash
npx degit neondatabase/examples/with-nextjs-drizzle-local-vercel ./with-nextjs-drizzle-local-vercel
```

Run the command below to copy the `.env.example` file:

```
cp .env.example .env
```

## Setup Docker

```bash
docker-compose up -d
```

## Store your Neon credentials

Store your Neon credentials in your `.env` file.

```
POSTGRES_URL="**********************************************************************************"
```

- `user` is the database user.
- `password` is the database user’s password.
- `endpoint_hostname` is the host with neon.tech as the [TLD](https://www.cloudflare.com/en-gb/learning/dns/top-level-domain/).
- `dbname` is the name of the database. “neondb” is the default database created with each Neon project.
- `?sslmode=require` an optional query parameter that enforces the [SSL](https://www.cloudflare.com/en-gb/learning/ssl/what-is-ssl/) mode while connecting to the Postgres instance for better security.

**Important**: To ensure the security of your data, never expose your Neon credentials to the browser.

Run the command below to install project dependencies:

```
npm install
```

Run the Next.js application using the following command:

```
npm run dev
```
