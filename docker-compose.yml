services:
   postgres:
      image: 'postgres:latest'
      environment:
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: postgres
        POSTGRES_DB: postgres
      ports:
      - '5432:5432'
   pg_proxy:
      image: ghcr.io/neondatabase/wsproxy:latest
      environment:
        APPEND_PORT: 'postgres:5432'
        ALLOW_ADDR_REGEX: '.*'
        LOG_TRAFFIC: 'true'
      ports:
      - '5433:80'
      depends_on:
      - postgres
