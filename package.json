{"name": "with-nextjs-drizzle-local-vercel", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "fmt": "prettier --write '**/*' --ignore-unknown", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate"}, "dependencies": {"@neondatabase/serverless": "^1.0.0", "autoprefixer": "10.4.16", "drizzle-orm": "0.38.1", "next": "^15.1.0", "postcss": "8.4.31", "postgres": "^3.4.5", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "3.3.3", "typescript": "5.2.2", "ws": "^8.18.0"}, "devDependencies": {"@types/react": "18.2.25", "@types/react-dom": "18.2.11", "@types/ws": "^8.5.12", "drizzle-kit": "^0.30.0", "prettier": "^3.3.3"}}